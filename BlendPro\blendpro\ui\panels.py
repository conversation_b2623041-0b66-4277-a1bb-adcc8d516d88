"""
BlendPro UI Panels
=================

Modern UI panels for BlendPro using Blender 4.2+ APIs.
"""

import bpy
from bpy.types import Panel
from ..utils.logger import get_logger


class BlendProPanel(Panel):
    """Main BlendPro panel in the 3D Viewport sidebar"""
    
    bl_label = "BlendPro AI Assistant"
    bl_idname = "BLENDPRO_PT_main_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'BlendPro'
    bl_options = {'DEFAULT_CLOSED'}
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.BlendProPanel")
    
    @classmethod
    def poll(cls, context):
        """Panel is always available in 3D viewport"""
        return context.space_data.type == 'VIEW_3D'
    
    def draw_header(self, context):
        """Draw panel header with icon"""
        layout = self.layout
        layout.label(text="", icon='CONSOLE')
    
    def draw(self, context):
        """Draw the main panel UI"""
        layout = self.layout
        scene = context.scene
        
        # Check if properties are initialized
        if not hasattr(scene, 'blendpro_settings'):
            layout.label(text="BlendPro not initialized", icon='ERROR')
            return
        
        settings = scene.blendpro_settings
        
        # Provider selection section
        self._draw_provider_section(layout, settings)
        
        # Chat history section
        self._draw_chat_section(layout, scene)
        
        # Input section
        self._draw_input_section(layout, scene)
        
        # Action buttons
        self._draw_action_buttons(layout, scene)
        
        # Status section
        self._draw_status_section(layout, settings)
    
    def _draw_provider_section(self, layout, settings):
        """Draw AI provider selection section"""
        box = layout.box()
        box.label(text="AI Provider", icon='NETWORK_DRIVE')
        
        # Provider selection
        row = box.row(align=True)
        row.prop(settings, "ai_provider", text="")
        row.operator("blendpro.settings", text="", icon='PREFERENCES')
        
        # Model selection
        if settings.ai_provider != 'none':
            box.prop(settings, "ai_model", text="Model")
            
            # Advanced settings (collapsible)
            if settings.show_advanced:
                col = box.column(align=True)
                col.prop(settings, "max_tokens", text="Max Tokens")
                col.prop(settings, "temperature", text="Temperature")
                col.prop(settings, "timeout", text="Timeout (s)")
        
        # Toggle advanced settings
        box.prop(settings, "show_advanced", text="Advanced Settings", icon='TRIA_DOWN' if settings.show_advanced else 'TRIA_RIGHT')
    
    def _draw_chat_section(self, layout, scene):
        """Draw chat history section"""
        box = layout.box()
        
        # Header with clear button
        row = box.row(align=True)
        row.label(text="Chat History", icon='CHAT')
        row.operator("blendpro.clear_chat", text="", icon='TRASH')
        
        # Chat messages
        if hasattr(scene, 'blendpro_chat_history') and scene.blendpro_chat_history:
            # Scrollable area for chat history
            col = box.column(align=True)
            
            # Show last few messages (limit for performance)
            messages = scene.blendpro_chat_history
            start_idx = max(0, len(messages) - 10)  # Show last 10 messages
            
            for i, message in enumerate(messages[start_idx:], start_idx):
                self._draw_message(col, message, i)
        else:
            box.label(text="No messages yet", icon='INFO')
    
    def _draw_message(self, layout, message, index):
        """Draw a single chat message"""
        if message.type == 'user':
            # User message
            row = layout.row(align=True)
            row.label(text="You:", icon='USER')
            row.operator("blendpro.delete_message", text="", icon='X', emboss=False).message_index = index
            
            # Message content (truncated if too long)
            content = message.content
            if len(content) > 50:
                content = content[:47] + "..."
            layout.label(text=content)
            
        elif message.type == 'assistant':
            # AI response
            row = layout.row(align=True)
            row.label(text="AI:", icon='CONSOLE')
            row.operator("blendpro.show_code", text="", icon='TEXT').code = message.content
            row.operator("blendpro.delete_message", text="", icon='X', emboss=False).message_index = index
            
            # Show first line of code
            lines = message.content.split('\n')
            first_line = lines[0] if lines else "Generated code"
            if len(first_line) > 40:
                first_line = first_line[:37] + "..."
            layout.label(text=first_line, icon='SCRIPTPLUGINS')
    
    def _draw_input_section(self, layout, scene):
        """Draw input section"""
        box = layout.box()
        box.label(text="Command Input", icon='EDITMODE_HLT')
        
        # Text input
        col = box.column(align=True)
        col.prop(scene, "blendpro_chat_input", text="")
        
        # Quick commands (optional)
        if hasattr(scene.blendpro_settings, 'show_quick_commands') and scene.blendpro_settings.show_quick_commands:
            row = col.row(align=True)
            row.scale_y = 0.8
            row.operator("blendpro.quick_command", text="Add Cube").command = "Create a cube at the origin"
            row.operator("blendpro.quick_command", text="Add Material").command = "Add a red material to selected object"
    
    def _draw_action_buttons(self, layout, scene):
        """Draw action buttons"""
        settings = scene.blendpro_settings
        
        # Main execute button
        row = layout.row(align=True)
        row.scale_y = 1.5
        
        if settings.is_processing:
            row.operator("blendpro.cancel", text="Cancel", icon='CANCEL')
        else:
            execute_op = row.operator("blendpro.execute", text="Generate & Execute", icon='PLAY')
            execute_op.auto_execute = True
        
        # Secondary buttons
        row = layout.row(align=True)
        if not settings.is_processing:
            row.operator("blendpro.execute", text="Generate Only", icon='FILE_TEXT').auto_execute = False
        
        # Options
        col = layout.column(align=True)
        col.scale_y = 0.8
        col.prop(settings, "auto_execute", text="Auto Execute Code")
        col.prop(settings, "save_history", text="Save Chat History")
    
    def _draw_status_section(self, layout, settings):
        """Draw status information"""
        if settings.last_error:
            box = layout.box()
            box.alert = True
            box.label(text="Error:", icon='ERROR')
            
            # Split long error messages
            error_text = settings.last_error
            if len(error_text) > 60:
                words = error_text.split()
                lines = []
                current_line = ""
                
                for word in words:
                    if len(current_line + word) > 60:
                        if current_line:
                            lines.append(current_line.strip())
                            current_line = word + " "
                        else:
                            lines.append(word)
                            current_line = ""
                    else:
                        current_line += word + " "
                
                if current_line:
                    lines.append(current_line.strip())
                
                for line in lines[:3]:  # Show max 3 lines
                    box.label(text=line)
                    
                if len(lines) > 3:
                    box.label(text="...")
            else:
                box.label(text=error_text)
        
        elif settings.is_processing:
            box = layout.box()
            box.label(text="Processing...", icon='TIME')
            
            # Progress bar (if available)
            if hasattr(settings, 'progress'):
                box.prop(settings, "progress", text="Progress", slider=True)
        
        elif settings.last_success:
            row = layout.row()
            row.alert = False
            row.label(text="✓ Code executed successfully", icon='CHECKMARK')


class BlendProQuickCommandOperator(bpy.types.Operator):
    """Quick command operator for common tasks"""
    
    bl_idname = "blendpro.quick_command"
    bl_label = "Quick Command"
    bl_description = "Execute a quick command"
    bl_options = {'REGISTER', 'UNDO'}
    
    command: bpy.props.StringProperty(
        name="Command",
        description="Command to execute",
        default=""
    )
    
    def execute(self, context):
        """Execute the quick command"""
        if self.command:
            context.scene.blendpro_chat_input = self.command
            # Optionally auto-execute
            if context.scene.blendpro_settings.auto_execute:
                bpy.ops.blendpro.execute('INVOKE_DEFAULT')
        
        return {'FINISHED'}
