# BlendPro AI Assistant

![BlendPro Logo](https://via.placeholder.com/800x200/1a1a1a/ffffff?text=BlendPro+AI+Assistant)

**Modern AI-powered Blender assistant with multi-model support**

BlendPro is a complete refactor of BlenderGPT-reference, built with the latest technologies and designed for Blender 4.2+ Extensions platform. It provides seamless integration with multiple AI models to generate Blender Python code from natural language commands.

## ✨ Features

### 🤖 Multi-AI Model Support
- **OpenAI**: GPT-4o, GPT-4o-mini, GPT-4-turbo, GPT-3.5-turbo
- **Anthropic**: Claude-3.5-Sonnet, Claude-3-Haiku
- **Google**: Gemini-1.5-Pro, Gemini-1.5-Flash

### 🎨 Modern UI/UX
- Clean, intuitive interface designed for Blender 4.2+
- Real-time code generation with progress indicators
- Syntax highlighting for generated code
- Chat history with search and filtering
- Customizable themes and layouts

### 🔧 Advanced Features
- **Smart Code Execution**: Safe execution with error handling
- **Code Templates**: Pre-built templates for common tasks
- **Project Management**: Save and organize AI-generated scripts
- **Batch Processing**: Execute multiple commands in sequence
- **Export Options**: Save code as .py files or Blender text blocks

### 🛡️ Security & Reliability
- Secure API key management
- Rate limiting and timeout handling
- Comprehensive error reporting
- Automatic retry mechanisms
- Input validation and sanitization

## 📋 Requirements

- **Blender**: 4.2.0 or later
- **Python**: 3.10+ (included with Blender)
- **API Keys**: At least one of:
  - OpenAI API key
  - Anthropic API key
  - Google AI API key

## 🚀 Installation

### Method 1: Blender Extensions Platform (Recommended)

1. Open Blender 4.2+
2. Go to `Edit > Preferences > Get Extensions`
3. Search for "BlendPro AI Assistant"
4. Click `Install` and enable the extension

### Method 2: Manual Installation

1. Download the latest release from [GitHub](https://github.com/inkbytefo/BlendPro/releases)
2. Open Blender and go to `Edit > Preferences > Add-ons`
3. Click `Install from Disk` and select the downloaded .zip file
4. Enable "BlendPro AI Assistant" in the add-ons list

### Method 3: Development Installation

```bash
git clone https://github.com/inkbytefo/BlendPro.git
cd BlendPro
# Install dependencies (optional, for development)
pip install -r requirements.txt
```

## ⚙️ Configuration

1. **Open BlendPro Panel**:
   - In 3D Viewport, press `N` to open sidebar
   - Navigate to `BlendPro` tab

2. **Configure API Keys**:
   - Go to `Edit > Preferences > Add-ons`
   - Find "BlendPro AI Assistant" and expand settings
   - Enter your API keys for desired AI providers

3. **Select AI Model**:
   - In BlendPro panel, choose your preferred AI provider and model
   - Adjust settings like temperature and max tokens as needed

## 🎯 Usage

### Basic Usage

1. **Open BlendPro Panel** in the 3D Viewport sidebar
2. **Select AI Model** from the dropdown menu
3. **Enter Command** in natural language, e.g.:
   - "Create 10 cubes in random positions"
   - "Add a material with metallic properties to selected objects"
   - "Create a simple animation rotating the default cube"
4. **Click Execute** to generate and run the code

### Advanced Features

#### Code Templates
- Access pre-built templates for common tasks
- Customize and save your own templates
- Share templates with the community

#### Project Management
- Organize scripts by project
- Version control for generated code
- Export projects as complete add-ons

#### Batch Processing
- Queue multiple commands
- Execute commands in sequence
- Set up complex workflows

## 📚 Examples

### Creating Objects
```
"Create a suzanne monkey head at position (2, 0, 0) with a red material"
```

### Animation
```
"Animate the selected object to rotate 360 degrees over 120 frames"
```

### Materials and Shading
```
"Create a glass material with blue tint and apply it to the selected object"
```

### Modeling Operations
```
"Add a subdivision surface modifier to all selected objects"
```

## 🔧 Development

### Project Structure
```
BlendPro/
├── __init__.py              # Main addon entry point
├── blender_manifest.toml    # Extension manifest
├── requirements.txt         # Python dependencies
├── README.md               # This file
└── blendpro/              # Main package
    ├── __init__.py
    ├── ai/                # AI model integrations
    ├── core/              # Core functionality
    ├── ui/                # User interface
    └── utils/             # Utilities and helpers
```

### Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make your changes and add tests
4. Commit your changes: `git commit -am 'Add feature'`
5. Push to the branch: `git push origin feature-name`
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Original BlenderGPT-reference project by Aarya Gadekar
- Blender Foundation for the amazing 3D software
- OpenAI, Anthropic, and Google for their AI APIs
- The Blender community for feedback and support

## 📞 Support

- **Documentation**: [GitHub Wiki](https://github.com/inkbytefo/BlendPro/wiki)
- **Issues**: [GitHub Issues](https://github.com/inkbytefo/BlendPro/issues)
- **Discussions**: [GitHub Discussions](https://github.com/inkbytefo/BlendPro/discussions)
- **Email**: <EMAIL>

---

**Made with ❤️ by [inkbytefo](https://github.com/inkbytefo)**
