"""
BlendPro Preferences
===================

Addon preferences for BlendPro with API key management and settings.
"""

import bpy
from bpy.types import AddonPreferences
from bpy.props import StringProperty, EnumProperty, BoolProperty, IntProperty, FloatProperty

from ..utils.logger import get_logger


class BlendProPreferences(AddonPreferences):
    """BlendPro addon preferences"""
    
    bl_idname = "blendpro_ai_assistant"  # Must match manifest id
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.BlendProPreferences")
    
    # API Keys
    openai_api_key: StringProperty(
        name="OpenAI API Key",
        description="Your OpenAI API key for GPT models",
        default="",
        subtype='PASSWORD',
        options={'SKIP_SAVE'}  # Don't save in .blend files for security
    )
    
    anthropic_api_key: StringProperty(
        name="Anthropic API Key", 
        description="Your Anthropic API key for Claude models",
        default="",
        subtype='PASSWORD',
        options={'SKIP_SAVE'}
    )
    
    google_api_key: StringProperty(
        name="Google API Key",
        description="Your Google API key for Gemini models", 
        default="",
        subtype='PASSWORD',
        options={'SKIP_SAVE'}
    )
    
    # Default Provider Settings
    default_provider: EnumProperty(
        name="Default AI Provider",
        description="Default AI provider to use",
        items=[
            ('openai', "OpenAI", "Use OpenAI GPT models"),
            ('anthropic', "Anthropic", "Use Anthropic Claude models"),
            ('google', "Google", "Use Google Gemini models"),
        ],
        default='openai'
    )
    
    # Model Settings
    openai_default_model: EnumProperty(
        name="OpenAI Model",
        description="Default OpenAI model",
        items=[
            ('gpt-4o', "GPT-4o", "Latest GPT-4o model"),
            ('gpt-4o-mini', "GPT-4o Mini", "Faster, cheaper GPT-4o"),
            ('gpt-4-turbo', "GPT-4 Turbo", "GPT-4 Turbo model"),
            ('gpt-3.5-turbo', "GPT-3.5 Turbo", "GPT-3.5 Turbo model"),
        ],
        default='gpt-4o-mini'
    )
    
    # Generation Settings
    default_max_tokens: IntProperty(
        name="Max Tokens",
        description="Maximum tokens for AI responses",
        default=2000,
        min=100,
        max=8000
    )
    
    default_temperature: FloatProperty(
        name="Temperature",
        description="AI response creativity (0.0 = deterministic, 1.0 = creative)",
        default=0.1,
        min=0.0,
        max=2.0,
        precision=2
    )
    
    # UI Settings
    auto_execute_code: BoolProperty(
        name="Auto Execute Code",
        description="Automatically execute generated code by default",
        default=False
    )
    
    save_chat_history: BoolProperty(
        name="Save Chat History",
        description="Save chat history between sessions",
        default=True
    )
    
    # Security Settings
    enable_code_review: BoolProperty(
        name="Enable Code Review",
        description="Show generated code before execution for review",
        default=True
    )
    
    def draw(self, context):
        """Draw the preferences UI"""
        layout = self.layout
        
        # API Keys Section
        self._draw_api_keys_section(layout)
        
        # Default Settings Section  
        self._draw_default_settings_section(layout)
        
        # UI Settings Section
        self._draw_ui_settings_section(layout)
        
        # Security Settings Section
        self._draw_security_settings_section(layout)
    
    def _draw_api_keys_section(self, layout):
        """Draw API keys configuration section"""
        box = layout.box()
        box.label(text="API Keys", icon='KEY_HLT')
        
        col = box.column(align=True)
        
        # OpenAI
        row = col.row(align=True)
        row.prop(self, "openai_api_key", text="OpenAI")
        if self.openai_api_key:
            row.label(text="✓", icon='CHECKMARK')
        else:
            row.label(text="✗", icon='X')
        
        # Anthropic
        row = col.row(align=True)
        row.prop(self, "anthropic_api_key", text="Anthropic")
        if self.anthropic_api_key:
            row.label(text="✓", icon='CHECKMARK')
        else:
            row.label(text="✗", icon='X')
        
        # Google
        row = col.row(align=True)
        row.prop(self, "google_api_key", text="Google")
        if self.google_api_key:
            row.label(text="✓", icon='CHECKMARK')
        else:
            row.label(text="✗", icon='X')
        
        # Help text
        col.separator()
        col.label(text="Get API keys from:", icon='INFO')
        col.label(text="• OpenAI: platform.openai.com")
        col.label(text="• Anthropic: console.anthropic.com")
        col.label(text="• Google: makersuite.google.com")
    
    def _draw_default_settings_section(self, layout):
        """Draw default settings section"""
        box = layout.box()
        box.label(text="Default Settings", icon='SETTINGS')
        
        # Provider and model selection
        col = box.column(align=True)
        col.prop(self, "default_provider")
        
        if self.default_provider == 'openai':
            col.prop(self, "openai_default_model", text="Model")
        
        # Generation parameters
        col.separator()
        col.prop(self, "default_max_tokens")
        col.prop(self, "default_temperature")
    
    def _draw_ui_settings_section(self, layout):
        """Draw UI settings section"""
        box = layout.box()
        box.label(text="UI Settings", icon='UI')
        
        col = box.column(align=True)
        col.prop(self, "auto_execute_code")
        col.prop(self, "save_chat_history")
    
    def _draw_security_settings_section(self, layout):
        """Draw security settings section"""
        box = layout.box()
        box.label(text="Security Settings", icon='LOCKED')
        
        col = box.column(align=True)
        col.prop(self, "enable_code_review")
        
        # Warning about code execution
        col.separator()
        warning_box = col.box()
        warning_box.alert = True
        warning_box.label(text="⚠ Warning:", icon='ERROR')
        warning_box.label(text="AI-generated code can modify your Blender scene.")
        warning_box.label(text="Always review code before execution.")
