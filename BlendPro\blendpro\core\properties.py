"""
BlendPro Properties
==================

Blender properties for BlendPro including scene properties,
chat history, and settings.
"""

import bpy
from bpy.props import (
    StringProperty, EnumProperty, BoolProperty, 
    IntProperty, FloatProperty, CollectionProperty
)
from bpy.types import PropertyGroup

from ..utils.logger import get_logger


class BlendProChatMessage(PropertyGroup):
    """Individual chat message property"""
    
    type: StringProperty(
        name="Type",
        description="Message type (user, assistant, system)",
        default="user"
    )
    
    content: StringProperty(
        name="Content", 
        description="Message content",
        default=""
    )
    
    timestamp: FloatProperty(
        name="Timestamp",
        description="Message timestamp",
        default=0.0
    )


class BlendProSettings(PropertyGroup):
    """BlendPro settings property group"""
    
    # AI Provider Settings
    ai_provider: EnumProperty(
        name="AI Provider",
        description="Current AI provider",
        items=[
            ('none', "None", "No provider selected"),
            ('openai', "OpenAI", "OpenAI GPT models"),
            ('anthropic', "Anthropic", "Anthropic Claude models"),
            ('google', "Google", "Google Gemini models"),
        ],
        default='none'
    )
    
    ai_model: EnumProperty(
        name="AI Model",
        description="Current AI model",
        items=lambda self, context: get_model_items(self, context),
        default=0
    )
    
    # Generation Settings
    max_tokens: IntProperty(
        name="Max Tokens",
        description="Maximum tokens for AI responses",
        default=2000,
        min=100,
        max=8000
    )
    
    temperature: FloatProperty(
        name="Temperature",
        description="AI response creativity",
        default=0.1,
        min=0.0,
        max=2.0,
        precision=2
    )
    
    timeout: IntProperty(
        name="Timeout",
        description="Request timeout in seconds",
        default=30,
        min=5,
        max=120
    )
    
    # UI Settings
    show_advanced: BoolProperty(
        name="Show Advanced",
        description="Show advanced settings",
        default=False
    )
    
    auto_execute: BoolProperty(
        name="Auto Execute",
        description="Automatically execute generated code",
        default=False
    )
    
    save_history: BoolProperty(
        name="Save History",
        description="Save chat history",
        default=True
    )
    
    max_history: IntProperty(
        name="Max History",
        description="Maximum chat history entries",
        default=50,
        min=10,
        max=200
    )
    
    # Status Properties
    is_processing: BoolProperty(
        name="Is Processing",
        description="Whether AI is currently processing",
        default=False
    )
    
    last_error: StringProperty(
        name="Last Error",
        description="Last error message",
        default=""
    )
    
    last_success: BoolProperty(
        name="Last Success",
        description="Whether last operation was successful",
        default=False
    )
    
    progress: FloatProperty(
        name="Progress",
        description="Current operation progress",
        default=0.0,
        min=0.0,
        max=1.0,
        subtype='PERCENTAGE'
    )


def get_model_items(self, context):
    """Get available models for current provider"""
    provider = self.ai_provider
    
    if provider == 'openai':
        return [
            ('gpt-4o', "GPT-4o", "Latest GPT-4o model"),
            ('gpt-4o-mini', "GPT-4o Mini", "Faster, cheaper GPT-4o"),
            ('gpt-4-turbo', "GPT-4 Turbo", "GPT-4 Turbo model"),
            ('gpt-3.5-turbo', "GPT-3.5 Turbo", "GPT-3.5 Turbo model"),
        ]
    elif provider == 'anthropic':
        return [
            ('claude-3-5-sonnet-20241022', "Claude 3.5 Sonnet", "Latest Claude 3.5 Sonnet"),
            ('claude-3-haiku-20240307', "Claude 3 Haiku", "Fast Claude 3 Haiku"),
        ]
    elif provider == 'google':
        return [
            ('gemini-1.5-pro', "Gemini 1.5 Pro", "Gemini 1.5 Pro model"),
            ('gemini-1.5-flash', "Gemini 1.5 Flash", "Fast Gemini 1.5 Flash"),
        ]
    else:
        return [('none', "No Provider", "No provider selected")]


def init_properties():
    """Initialize BlendPro properties"""
    logger = get_logger(f"{__name__}.init_properties")
    
    try:
        # Register property groups
        bpy.utils.register_class(BlendProChatMessage)
        bpy.utils.register_class(BlendProSettings)
        
        # Scene properties
        bpy.types.Scene.blendpro_settings = bpy.props.PointerProperty(
            type=BlendProSettings,
            name="BlendPro Settings",
            description="BlendPro settings and configuration"
        )
        
        bpy.types.Scene.blendpro_chat_history = CollectionProperty(
            type=BlendProChatMessage,
            name="Chat History",
            description="BlendPro chat history"
        )
        
        bpy.types.Scene.blendpro_chat_input = StringProperty(
            name="Chat Input",
            description="Current chat input text",
            default="",
            maxlen=2048
        )
        
        logger.info("BlendPro properties initialized successfully")
        
    except Exception as e:
        logger.error(f"Failed to initialize properties: {e}")
        raise


def clear_properties():
    """Clear BlendPro properties"""
    logger = get_logger(f"{__name__}.clear_properties")
    
    try:
        # Remove scene properties
        if hasattr(bpy.types.Scene, 'blendpro_settings'):
            del bpy.types.Scene.blendpro_settings
        
        if hasattr(bpy.types.Scene, 'blendpro_chat_history'):
            del bpy.types.Scene.blendpro_chat_history
        
        if hasattr(bpy.types.Scene, 'blendpro_chat_input'):
            del bpy.types.Scene.blendpro_chat_input
        
        # Unregister property groups
        try:
            bpy.utils.unregister_class(BlendProSettings)
            bpy.utils.unregister_class(BlendProChatMessage)
        except Exception:
            pass  # May already be unregistered
        
        logger.info("BlendPro properties cleared successfully")
        
    except Exception as e:
        logger.error(f"Failed to clear properties: {e}")


def get_settings(context=None):
    """Get BlendPro settings from current scene"""
    if context is None:
        context = bpy.context
    
    if hasattr(context.scene, 'blendpro_settings'):
        return context.scene.blendpro_settings
    
    return None


def get_chat_history(context=None):
    """Get chat history from current scene"""
    if context is None:
        context = bpy.context
    
    if hasattr(context.scene, 'blendpro_chat_history'):
        return context.scene.blendpro_chat_history
    
    return None


def add_chat_message(message_type, content, context=None):
    """Add a message to chat history"""
    if context is None:
        context = bpy.context
    
    history = get_chat_history(context)
    if history is not None:
        message = history.add()
        message.type = message_type
        message.content = content
        message.timestamp = bpy.utils.time.time()
        
        # Limit history size
        settings = get_settings(context)
        if settings and len(history) > settings.max_history:
            history.remove(0)
        
        return message
    
    return None
