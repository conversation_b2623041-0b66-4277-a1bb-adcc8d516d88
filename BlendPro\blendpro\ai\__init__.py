"""
BlendPro AI Package
==================

AI model integrations for BlendPro supporting multiple providers:
- OpenAI (GPT-4o, GPT-4o-mini, GPT-4-turbo, GPT-3.5-turbo)
- Anthropic (Claude-3.5-<PERSON><PERSON>, <PERSON>-3-<PERSON><PERSON>)
- Google (Gemini-1.5-Pro, Gemini-1.5-Flash)
"""

from .base import AIProvider, AIResponse, AIError
from .openai_provider import OpenAIProvider
from .anthropic_provider import AnthropicProvider
from .google_provider import GoogleProvider
from .manager import AIManager

__all__ = [
    "AIProvider",
    "AIResponse", 
    "AIError",
    "OpenAIProvider",
    "AnthropicProvider", 
    "GoogleProvider",
    "AIManager",
]
